package com.bxm.customer.service;

import com.bxm.customer.domain.dto.valueAdded.BatchOperationRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationResultDTO;
import com.bxm.customer.domain.enums.ValueAddedBatchOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

/**
 * 批量操作服务测试类
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@SpringBootTest
public class BatchOperationServiceTest {

    @Autowired
    private IBatchOperationService batchOperationService;

    @Test
    public void testDetermineBatchReturnTargetStatus() {
        // 测试批量退回的目标状态确定
        String targetStatus1 = batchOperationService.determineBatchReturnTargetStatus(
                ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode());
        assert ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode().equals(targetStatus1);

        String targetStatus2 = batchOperationService.determineBatchReturnTargetStatus(
                ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode());
        assert ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode().equals(targetStatus2);

        String targetStatus3 = batchOperationService.determineBatchReturnTargetStatus(
                ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION.getCode());
        assert ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode().equals(targetStatus3);

        String targetStatus4 = batchOperationService.determineBatchReturnTargetStatus(
                ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode());
        assert ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT.getCode().equals(targetStatus4);
    }

    @Test
    public void testValidateBatchOperationRequest() {
        // 测试批量操作请求验证
        BatchOperationRequestDTO request = BatchOperationRequestDTO.builder()
                .deliveryOrderNos(Arrays.asList("VAD2508051430001A1C", "VAD2508051430002A1C"))
                .operationType(ValueAddedBatchOperationType.BATCH_RETURN)
                .sourceStatus(ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode())
                .operatorId(1001L)
                .operatorName("测试用户")
                .businessTopDeptId(100L)
                .build();

        // 应该不抛出异常
        batchOperationService.validateBatchOperationRequest(request);
    }

    @Test
    public void testValidateBatchOperationRequestWithoutSourceStatus() {
        // 测试批量退回操作缺少前置状态的情况
        BatchOperationRequestDTO request = BatchOperationRequestDTO.builder()
                .deliveryOrderNos(Arrays.asList("VAD2508051430001A1C"))
                .operationType(ValueAddedBatchOperationType.BATCH_RETURN)
                .operatorId(1001L)
                .operatorName("测试用户")
                .businessTopDeptId(100L)
                .build();

        try {
            batchOperationService.validateBatchOperationRequest(request);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            assert "批量退回操作必须指定前置状态".equals(e.getMessage());
        }
    }
}

package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量操作结果DTO
 *
 * 用于返回批量操作的执行结果
 * 包含成功数量、失败数量、错误数据批次号等信息
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作结果DTO")
public class BatchOperationResultDTO {

    /**
     * 批次号（用于导出异常数据）
     */
    @ApiModelProperty(value = "批次号，用于导出异常数据")
    private String batchNo;

    /**
     * 操作类型描述
     */
    @ApiModelProperty(value = "操作类型描述")
    private String operationDescription;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private Integer errorCount;

    /**
     * 成功的交付单编号列表
     */
    @ApiModelProperty(value = "成功的交付单编号列表")
    private List<String> successOrderNos;

    /**
     * 失败的交付单编号列表
     */
    @ApiModelProperty(value = "失败的交付单编号列表")
    private List<String> errorOrderNos;

    /**
     * 操作开始时间
     */
    @ApiModelProperty(value = "操作开始时间")
    private String startTime;

    /**
     * 操作结束时间
     */
    @ApiModelProperty(value = "操作结束时间")
    private String endTime;

    /**
     * 操作耗时（毫秒）
     */
    @ApiModelProperty(value = "操作耗时（毫秒）")
    private Long duration;

    /**
     * 是否有异常数据
     */
    @ApiModelProperty(value = "是否有异常数据")
    private Boolean hasErrors;

    /**
     * 操作结果摘要
     */
    @ApiModelProperty(value = "操作结果摘要")
    private String summary;

    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }

    /**
     * 获取操作结果摘要
     */
    public String getSummary() {
        if (summary != null) {
            return summary;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(operationDescription).append("完成，");
        sb.append("总计").append(totalCount).append("条，");
        sb.append("成功").append(successCount).append("条，");
        sb.append("失败").append(errorCount).append("条");

        if (errorCount > 0) {
            sb.append("，可通过批次号[").append(batchNo).append("]导出异常数据");
        }

        return sb.toString();
    }

    /**
     * 判断是否全部成功
     */
    public boolean isAllSuccess() {
        return errorCount == null || errorCount == 0;
    }

    /**
     * 判断是否全部失败
     */
    public boolean isAllFailed() {
        return successCount == null || successCount == 0;
    }
}

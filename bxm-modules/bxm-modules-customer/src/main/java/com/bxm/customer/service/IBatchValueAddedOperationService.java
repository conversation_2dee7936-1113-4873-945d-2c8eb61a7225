package com.bxm.customer.service;

import com.bxm.customer.domain.dto.valueAdded.BatchOperationErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationResultDTO;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量操作服务接口
 *
 * 提供增值交付单的各种批量操作功能
 * 包括批量确认、提交、关闭、处理异常、驳回、退回等操作
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
public interface IBatchValueAddedOperationService {

    /**
     * 执行批量操作
     *
     * 根据请求中的操作类型，对指定的交付单列表执行相应的批量操作
     * 操作过程中的异常信息会被记录并缓存到Redis中，可通过批次号导出
     *
     * @param request 批量操作请求，包含交付单列表、操作类型、操作人等信息
     * @return 批量操作结果，包含成功数量、失败数量、批次号等信息
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    BatchOperationResultDTO executeBatchOperation(@Valid @NotNull BatchOperationRequestDTO request);

    /**
     * 获取批量操作异常数据
     *
     * 根据批次号从Redis中获取批量操作过程中产生的异常数据
     * 用于异常数据的查看和导出功能
     *
     * @param batchNo 批次号，由批量操作时生成的唯一标识
     * @return 异常数据列表，如果批次号不存在或已过期则返回空列表
     * @throws IllegalArgumentException 当批次号为空时抛出
     */
    List<BatchOperationErrorDTO> getBatchOperationErrors(@NotBlank String batchNo);


    /**
     * 验证批量操作请求
     *
     * 对批量操作请求进行业务层面的验证
     * 包括交付单存在性验证、状态一致性验证、权限验证等
     *
     * @param request 批量操作请求
     * @throws IllegalArgumentException 当验证失败时抛出，包含具体的失败原因
     */
    void validateBatchOperationRequest(@Valid @NotNull BatchOperationRequestDTO request);

    /**
     * 获取批量退回的目标状态
     *
     * 根据前置状态确定批量退回操作的目标状态
     * 支持多级退回：已扣款→待扣款，待扣款→待确认，待确认→待交付，待交付→待提交
     *
     * @param sourceStatus 前置状态代码
     * @return 目标状态代码
     * @throws IllegalArgumentException 当前置状态不支持退回操作时抛出
     */
    String determineBatchReturnTargetStatus(@NotBlank String sourceStatus);
}

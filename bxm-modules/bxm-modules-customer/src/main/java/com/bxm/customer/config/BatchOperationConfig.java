package com.bxm.customer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import lombok.Data;

/**
 * 批量操作配置类
 *
 * 用于配置批量操作相关的参数，如最大批量数量、缓存时间等
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Component
@Configuration
@ConfigurationProperties(prefix = "bxm.batch-operation")
public class BatchOperationConfig {

    /**
     * 单次批量操作最大支持的记录数
     */
    private Integer maxBatchSize = 1000;

    /**
     * 异常数据在Redis中的缓存时间（秒）
     */
    private Integer errorDataCacheTimeSeconds = 3600; // 1小时

    /**
     * Redis分批存储时每批的大小
     */
    private Integer redisBatchSize = 500;

    /**
     * 是否启用批量操作功能
     */
    private Boolean enabled = true;

    /**
     * 是否启用并发处理
     */
    private Boolean concurrentEnabled = false;

    /**
     * 并发处理的线程池大小
     */
    private Integer concurrentThreadPoolSize = 10;

    /**
     * 批量操作超时时间（毫秒）
     */
    private Long timeoutMillis = 300000L; // 5分钟

    /**
     * 是否启用详细日志
     */
    private Boolean verboseLogging = false;

    /**
     * 异常数据导出文件名前缀
     */
    private String exportFileNamePrefix = "批量操作异常数据";

    /**
     * 支持的导出格式
     */
    private String[] supportedExportFormats = {"xlsx", "csv"};

    /**
     * 验证配置参数的有效性
     */
    public void validate() {
        if (maxBatchSize <= 0 || maxBatchSize > 10000) {
            throw new IllegalArgumentException("maxBatchSize must be between 1 and 10000");
        }
        
        if (errorDataCacheTimeSeconds <= 0) {
            throw new IllegalArgumentException("errorDataCacheTimeSeconds must be positive");
        }
        
        if (redisBatchSize <= 0 || redisBatchSize > maxBatchSize) {
            throw new IllegalArgumentException("redisBatchSize must be positive and not greater than maxBatchSize");
        }
        
        if (concurrentThreadPoolSize <= 0 || concurrentThreadPoolSize > 100) {
            throw new IllegalArgumentException("concurrentThreadPoolSize must be between 1 and 100");
        }
        
        if (timeoutMillis <= 0) {
            throw new IllegalArgumentException("timeoutMillis must be positive");
        }
    }

    /**
     * 获取格式化的缓存时间描述
     */
    public String getFormattedCacheTime() {
        if (errorDataCacheTimeSeconds < 60) {
            return errorDataCacheTimeSeconds + "秒";
        } else if (errorDataCacheTimeSeconds < 3600) {
            return (errorDataCacheTimeSeconds / 60) + "分钟";
        } else {
            return (errorDataCacheTimeSeconds / 3600) + "小时";
        }
    }

    /**
     * 获取格式化的超时时间描述
     */
    public String getFormattedTimeout() {
        if (timeoutMillis < 1000) {
            return timeoutMillis + "毫秒";
        } else if (timeoutMillis < 60000) {
            return (timeoutMillis / 1000) + "秒";
        } else {
            return (timeoutMillis / 60000) + "分钟";
        }
    }

    /**
     * 检查是否支持指定的导出格式
     */
    public boolean isSupportedExportFormat(String format) {
        if (format == null || supportedExportFormats == null) {
            return false;
        }
        
        for (String supportedFormat : supportedExportFormats) {
            if (supportedFormat.equalsIgnoreCase(format)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format(
                "BatchOperationConfig{enabled=%s, maxBatchSize=%d, cacheTime=%s, timeout=%s, concurrent=%s}",
                enabled, maxBatchSize, getFormattedCacheTime(), getFormattedTimeout(), concurrentEnabled
        );
    }
}

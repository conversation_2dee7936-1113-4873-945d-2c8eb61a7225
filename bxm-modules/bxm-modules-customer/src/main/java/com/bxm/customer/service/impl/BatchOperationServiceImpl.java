package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.*;
import com.bxm.customer.domain.enums.ValueAddedBatchOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.IBatchOperationService;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 批量操作服务实现类
 *
 * 提供增值交付单的各种批量操作功能实现
 * 包括批量确认、提交、关闭、处理异常、驳回、退回等操作
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Service
public class BatchOperationServiceImpl implements IBatchOperationService {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private RedisService redisService;

    @Override
    public BatchOperationResultDTO executeBatchOperation(BatchOperationRequestDTO request) {
        log.info("Starting batch operation: {}, order count: {}", 
                request.getOperationType().getDescription(), request.getOrderCount());
        
        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        // 验证请求参数
        validateBatchOperationRequest(request);
        
        // 批量查询交付单
        List<ValueAddedDeliveryOrder> orders = batchQueryOrders(request.getDeliveryOrderNos());
        
        // 执行批量操作
        BatchOperationResult result = processBatchOperation(orders, request);
        
        // 缓存异常数据
        String batchNo = null;
        if (!result.getErrors().isEmpty()) {
            batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            cacheErrorData(batchNo, result.getErrors());
        }
        
        long endTime = System.currentTimeMillis();
        String endTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        // 构建返回结果
        BatchOperationResultDTO resultDTO = BatchOperationResultDTO.builder()
                .batchNo(batchNo)
                .operationDescription(request.getOperationDescription())
                .totalCount(orders.size())
                .successCount(result.getSuccessOrderNos().size())
                .errorCount(result.getErrors().size())
                .successOrderNos(result.getSuccessOrderNos())
                .errorOrderNos(result.getErrors().stream()
                        .map(BatchOperationErrorDTO::getDeliveryOrderNo)
                        .collect(Collectors.toList()))
                .startTime(startTimeStr)
                .endTime(endTimeStr)
                .duration(endTime - startTime)
                .hasErrors(!result.getErrors().isEmpty())
                .build();
        
        log.info("Batch operation completed: {}, total: {}, success: {}, error: {}, duration: {}ms", 
                request.getOperationType().getDescription(), 
                resultDTO.getTotalCount(), resultDTO.getSuccessCount(), 
                resultDTO.getErrorCount(), resultDTO.getDuration());
        
        return resultDTO;
    }

    @Override
    public List<BatchOperationErrorDTO> getBatchOperationErrors(String batchNo) {
        if (batchNo == null || batchNo.trim().isEmpty()) {
            throw new IllegalArgumentException("批次号不能为空");
        }
        
        List<BatchOperationErrorDTO> errorList = redisService.getLargeCacheList(
                CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo, 500);
        
        return ObjectUtils.isEmpty(errorList) ? new ArrayList<>() : errorList;
    }

    @Override
    public boolean clearBatchOperationErrors(String batchNo) {
        if (batchNo == null || batchNo.trim().isEmpty()) {
            throw new IllegalArgumentException("批次号不能为空");
        }
        
        try {
            // 清理分批缓存的数据
            int batchIndex = 0;
            boolean hasData = true;
            
            while (hasData) {
                String key = CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo + ":" + batchIndex;
                if (redisService.hasKey(key)) {
                    redisService.deleteObject(key);
                    batchIndex++;
                } else {
                    hasData = false;
                }
            }
            
            log.info("Cleared batch operation error data for batchNo: {}", batchNo);
            return true;
        } catch (Exception e) {
            log.error("Failed to clear batch operation error data for batchNo: {}", batchNo, e);
            return false;
        }
    }

    @Override
    public void validateBatchOperationRequest(BatchOperationRequestDTO request) {
        // 验证批量退回操作的前置状态
        request.validateBatchReturnSourceStatus();
        
        // 验证交付单数量限制
        if (request.getOrderCount() > 1000) {
            throw new IllegalArgumentException("单次批量操作最多支持1000条记录");
        }
        
        // 验证交付单编号格式
        for (String orderNo : request.getDeliveryOrderNos()) {
            if (orderNo == null || orderNo.trim().isEmpty()) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }
        }
    }

    @Override
    public String determineBatchReturnTargetStatus(String sourceStatus) {
        if (sourceStatus == null || sourceStatus.trim().isEmpty()) {
            throw new IllegalArgumentException("前置状态不能为空");
        }
        
        ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(sourceStatus);
        if (currentStatus == null) {
            throw new IllegalArgumentException("无效的前置状态: " + sourceStatus);
        }
        
        // 定义退回规则映射
        Map<ValueAddedDeliveryOrderStatus, ValueAddedDeliveryOrderStatus> returnMapping = Map.of(
                ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED, ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION,
                ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION, ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION,
                ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION, ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY,
                ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT
        );
        
        ValueAddedDeliveryOrderStatus targetStatus = returnMapping.get(currentStatus);
        if (targetStatus == null) {
            throw new IllegalArgumentException("状态 " + currentStatus.getDescription() + " 不支持退回操作");
        }
        
        return targetStatus.getCode();
    }

    /**
     * 批量查询交付单
     */
    private List<ValueAddedDeliveryOrder> batchQueryOrders(List<String> deliveryOrderNos) {
        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNos);
        queryWrapper.eq(ValueAddedDeliveryOrder::getIsDel, false);
        
        List<ValueAddedDeliveryOrder> orders = valueAddedDeliveryOrderService.list(queryWrapper);
        
        // 检查是否有交付单不存在
        Set<String> foundOrderNos = orders.stream()
                .map(ValueAddedDeliveryOrder::getDeliveryOrderNo)
                .collect(Collectors.toSet());
        
        List<String> notFoundOrderNos = deliveryOrderNos.stream()
                .filter(orderNo -> !foundOrderNos.contains(orderNo))
                .collect(Collectors.toList());
        
        if (!notFoundOrderNos.isEmpty()) {
            throw new IllegalArgumentException("以下交付单不存在: " + String.join(", ", notFoundOrderNos));
        }
        
        return orders;
    }

    /**
     * 处理批量操作
     */
    private BatchOperationResult processBatchOperation(List<ValueAddedDeliveryOrder> orders, 
                                                     BatchOperationRequestDTO request) {
        List<BatchOperationErrorDTO> errors = new ArrayList<>();
        List<String> successOrderNos = new ArrayList<>();
        
        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 确定目标状态
                String targetStatus = determineTargetStatus(order, request);
                
                // 构建状态变更请求
                StatusChangeRequestDTO changeRequest = buildStatusChangeRequest(order, targetStatus, request);
                
                // 执行状态变更
                valueAddedDeliveryOrderService.changeStatus(changeRequest);
                
                successOrderNos.add(order.getDeliveryOrderNo());
                
                log.debug("Batch operation success for order: {} from {} to {}", 
                        order.getDeliveryOrderNo(), order.getStatus(), targetStatus);
                
            } catch (Exception e) {
                log.warn("Batch operation failed for order: {}, error: {}", 
                        order.getDeliveryOrderNo(), e.getMessage());
                
                errors.add(buildErrorData(order, request, e.getMessage()));
            }
        }
        
        return new BatchOperationResult(successOrderNos, errors);
    }

    /**
     * 确定目标状态
     */
    private String determineTargetStatus(ValueAddedDeliveryOrder order, BatchOperationRequestDTO request) {
        if (request.getOperationType().isBatchReturn()) {
            // 批量退回需要动态确定目标状态
            return determineBatchReturnTargetStatus(request.getSourceStatus());
        } else {
            // 其他操作类型有固定的目标状态
            return request.getOperationType().getTargetStatus().getCode();
        }
    }

    /**
     * 构建状态变更请求
     */
    private StatusChangeRequestDTO buildStatusChangeRequest(ValueAddedDeliveryOrder order, 
                                                          String targetStatus, 
                                                          BatchOperationRequestDTO request) {
        return StatusChangeRequestDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .targetStatus(targetStatus)
                .reason(request.getReason())
                .operatorId(request.getOperatorId())
                .operatorName(request.getOperatorName())
                .remark(request.getRemark())
                .businessTopDeptId(request.getBusinessTopDeptId())
                .creditCode(order.getCreditCode())
                .build();
    }

    /**
     * 构建错误数据
     */
    private BatchOperationErrorDTO buildErrorData(ValueAddedDeliveryOrder order, 
                                                BatchOperationRequestDTO request, 
                                                String errorMessage) {
        String targetStatus = null;
        try {
            targetStatus = determineTargetStatus(order, request);
        } catch (Exception e) {
            targetStatus = "无法确定";
        }
        
        return BatchOperationErrorDTO.createError(
                order.getDeliveryOrderNo(),
                order.getCreditCode(),
                order.getBusinessTopDeptId(),
                order.getCustomerName(),
                order.getTitle(),
                order.getStatus(),
                targetStatus,
                request.getOperationType().getDescription(),
                errorMessage,
                request.getOperatorName()
        );
    }

    /**
     * 缓存异常数据
     */
    private void cacheErrorData(String batchNo, List<BatchOperationErrorDTO> errors) {
        try {
            redisService.setLargeCacheList(
                    CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo, 
                    errors, 500, 60 * 60, TimeUnit.SECONDS
            );
            log.info("Cached {} error records with batchNo: {}", errors.size(), batchNo);
        } catch (Exception e) {
            log.error("Failed to cache error data for batchNo: {}", batchNo, e);
        }
    }

    /**
     * 批量操作结果内部类
     */
    private static class BatchOperationResult {
        private final List<String> successOrderNos;
        private final List<BatchOperationErrorDTO> errors;
        
        public BatchOperationResult(List<String> successOrderNos, List<BatchOperationErrorDTO> errors) {
            this.successOrderNos = successOrderNos;
            this.errors = errors;
        }
        
        public List<String> getSuccessOrderNos() {
            return successOrderNos;
        }
        
        public List<BatchOperationErrorDTO> getErrors() {
            return errors;
        }
    }
}

package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 批量操作类型枚举
 *
 * 定义增值交付单支持的各种批量操作类型
 * 每种操作类型对应特定的状态转换规则
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Getter
@AllArgsConstructor
public enum ValueAddedBatchOperationType {

    /**
     * 批量确认 - 提交人确认交付单
     * 状态转换：已交付待确认 → 已确认待扣款
     */
    BATCH_CONFIRM("BATCH_CONFIRM", "批量确认",
                  ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION,
                  ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION),

    /**
     * 批量提交 - 提交人提交交付单
     * 状态转换：已保存待提交 → 已提交待交付
     */
    BATCH_SUBMIT("BATCH_SUBMIT", "批量提交",
                 ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT,
                 ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY),

    /**
     * 批量关闭扣款 - 提交人关闭扣款
     * 状态转换：扣款异常(待确认) → 已关闭扣款
     */
    BATCH_CLOSE_DEDUCTION("BATCH_CLOSE_DEDUCTION", "批量关闭扣款",
                          ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION,
                          ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED),

    /**
     * 批量关闭交付 - 提交人关闭交付
     * 状态转换：交付异常(待确认) → 已关闭交付
     */
    BATCH_CLOSE_DELIVERY("BATCH_CLOSE_DELIVERY", "批量关闭交付",
                         ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION,
                         ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED),

    /**
     * 批量解除扣款异常 - 提交人批量解除扣款异常
     * 状态转换：扣款异常(待确认) → 已确认待扣款
     */
    BATCH_RESOLVE_DEDUCTION_EXCEPTION("BATCH_RESOLVE_DEDUCTION_EXCEPTION", "批量解除扣款异常",
                                      ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION,
                                      ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION),

    /**
     * 批量解除交付异常 - 提交人批量解除交付异常
     * 状态转换：交付异常(待确认) → 已交付待确认
     */
    BATCH_RESOLVE_DELIVERY_EXCEPTION("BATCH_RESOLVE_DELIVERY_EXCEPTION", "批量解除交付异常",
                                     ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION,
                                     ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION),

    /**
     * 批量驳回 - 前置条件是待确认，目标状态是待交付
     * 状态转换：已交付待确认 → 已提交待交付
     */
    BATCH_REJECT("BATCH_REJECT", "批量驳回",
                 ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION,
                 ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY),

    /**
     * 批量退回 - 支持多级退回，目标状态根据前置状态动态确定
     * 支持的退回路径：
     * 已扣款 → 待扣款
     * 待扣款 → 待确认
     * 待确认 → 待交付
     * 待交付 → 待提交
     */
    BATCH_RETURN("BATCH_RETURN", "批量退回", null, null);

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String description;

    /**
     * 源状态（批量退回时为null，需要动态确定）
     */
    private final ValueAddedDeliveryOrderStatus sourceStatus;

    /**
     * 目标状态（批量退回时为null，需要动态确定）
     */
    private final ValueAddedDeliveryOrderStatus targetStatus;

    /**
     * 根据操作类型代码获取枚举
     *
     * @param code 操作类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ValueAddedBatchOperationType getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ValueAddedBatchOperationType type : values()) {
            if (type.getCode().equals(code.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证操作类型代码是否有效
     *
     * @param code 操作类型代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为批量退回操作
     *
     * @return 是否为批量退回操作
     */
    public boolean isBatchReturn() {
        return this == BATCH_RETURN;
    }

    /**
     * 判断是否需要动态确定目标状态
     *
     * @return 是否需要动态确定目标状态
     */
    public boolean needsDynamicTargetStatus() {
        return this.targetStatus == null;
    }
}

# 增值交付单批量操作系统

## 概述

增值交付单批量操作系统提供了对增值交付单的各种批量操作功能，包括批量确认、提交、关闭、处理异常、驳回、退回等操作。系统设计遵循优雅可扩展的原则，复用现有的状态机逻辑，确保数据一致性和系统可维护性。

## 功能特性

### 支持的批量操作类型

1. **批量确认** (`BATCH_CONFIRM`)
   - 状态转换：已交付待确认 → 已确认待扣款
   - 适用场景：提交人确认交付单

2. **批量提交** (`BATCH_SUBMIT`)
   - 状态转换：已保存待提交 → 已提交待交付
   - 适用场景：提交人提交交付单

3. **批量关闭扣款** (`BATCH_CLOSE_DEDUCTION`)
   - 状态转换：扣款异常(待确认) → 已关闭扣款
   - 适用场景：提交人关闭扣款

4. **批量关闭交付** (`BATCH_CLOSE_DELIVERY`)
   - 状态转换：交付异常(待确认) → 已关闭交付
   - 适用场景：提交人关闭交付

5. **批量解除扣款异常** (`BATCH_RESOLVE_DEDUCTION_EXCEPTION`)
   - 状态转换：扣款异常(待确认) → 已确认待扣款
   - 适用场景：提交人批量解除扣款异常

6. **批量解除交付异常** (`BATCH_RESOLVE_DELIVERY_EXCEPTION`)
   - 状态转换：交付异常(待确认) → 已交付待确认
   - 适用场景：提交人批量解除交付异常

7. **批量驳回** (`BATCH_REJECT`)
   - 状态转换：已交付待确认 → 已提交待交付
   - 适用场景：前置条件是待确认，目标状态是待交付

8. **批量退回** (`BATCH_RETURN`)
   - 支持多级退回，目标状态根据前置状态动态确定
   - 支持的退回路径：
     - 已扣款 → 待扣款
     - 待扣款 → 待确认
     - 待确认 → 待交付
     - 待交付 → 待提交

### 核心特性

- **状态机复用**：完全复用现有的状态机管理器和策略模式
- **事务安全**：每个交付单的状态变更在独立事务中处理
- **异常处理**：详细记录失败信息，支持异常数据导出
- **性能优化**：批量查询减少数据库交互，Redis缓存异常数据
- **可扩展性**：新增批量操作类型只需添加枚举和映射关系

## API接口

### 1. 批量操作接口

**接口地址**：`POST /customer/valueAdded/deliveryOrder/batchOperation`

**请求参数**：
```json
{
  "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
  "operationType": "BATCH_CONFIRM",
  "sourceStatus": "DEDUCTION_COMPLETED",  // 批量退回时必填
  "reason": "批量确认交付单",
  "operatorId": 1001,
  "operatorName": "张三",
  "businessTopDeptId": 100,
  "remark": "批量确认操作"
}
```

**响应结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "batchNo": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
    "operationDescription": "批量确认",
    "totalCount": 2,
    "successCount": 1,
    "errorCount": 1,
    "successOrderNos": ["VAD2508051430001A1C"],
    "errorOrderNos": ["VAD2508051430002A1C"],
    "startTime": "2025-08-17 10:30:00",
    "endTime": "2025-08-17 10:30:05",
    "duration": 5000,
    "hasErrors": true,
    "summary": "批量确认完成，总计2条，成功1条，失败1条，可通过批次号[a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6]导出异常数据"
  }
}
```

### 2. 导出异常数据接口

**接口地址**：`GET /customer/valueAdded/deliveryOrder/exportBatchErrors/{batchNo}`

**功能说明**：根据批次号导出批量操作过程中产生的异常数据为Excel文件

## 使用示例

### 批量确认示例

```bash
curl -X POST "http://localhost:8080/customer/valueAdded/deliveryOrder/batchOperation" \
  -H "Content-Type: application/json" \
  -d '{
    "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
    "operationType": "BATCH_CONFIRM",
    "reason": "批量确认交付单",
    "operatorId": 1001,
    "operatorName": "张三",
    "businessTopDeptId": 100
  }'
```

### 批量退回示例

```bash
curl -X POST "http://localhost:8080/customer/valueAdded/deliveryOrder/batchOperation" \
  -H "Content-Type: application/json" \
  -d '{
    "deliveryOrderNos": ["VAD2508051430001A1C", "VAD2508051430002A1C"],
    "operationType": "BATCH_RETURN",
    "sourceStatus": "DEDUCTION_COMPLETED",
    "reason": "批量退回交付单",
    "operatorId": 1001,
    "operatorName": "张三",
    "businessTopDeptId": 100
  }'
```

### 导出异常数据示例

```bash
curl -X GET "http://localhost:8080/customer/valueAdded/deliveryOrder/exportBatchErrors/a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6" \
  -H "Accept: application/vnd.ms-excel"
```

## 技术实现

### 核心组件

1. **BatchOperationService**：批量操作核心服务
2. **ValueAddedBatchOperationType**：批量操作类型枚举
3. **BatchOperationRequestDTO**：批量操作请求DTO
4. **BatchOperationResultDTO**：批量操作结果DTO
5. **BatchOperationErrorDTO**：批量操作异常数据DTO

### 处理流程

1. **请求验证**：验证批量操作请求参数
2. **批量查询**：根据交付单编号批量查询交付单
3. **状态转换**：逐个处理每个交付单的状态变更
4. **异常处理**：记录失败的交付单信息
5. **结果缓存**：将异常数据缓存到Redis
6. **返回结果**：返回批量操作结果

### 错误处理

- 每个交付单的状态变更在独立事务中处理
- 失败的交付单不影响其他交付单的处理
- 详细记录失败原因和上下文信息
- 支持通过批次号导出异常数据

## 注意事项

1. **数量限制**：单次批量操作最多支持1000条记录
2. **权限控制**：需要相应的操作权限
3. **状态验证**：会验证交付单当前状态是否符合操作要求
4. **数据缓存**：异常数据在Redis中缓存1小时
5. **批量退回**：必须指定前置状态参数

## 扩展指南

### 新增批量操作类型

1. 在 `ValueAddedBatchOperationType` 枚举中添加新的操作类型
2. 定义源状态和目标状态的映射关系
3. 如需特殊处理逻辑，可在 `BatchOperationServiceImpl` 中扩展

### 自定义异常数据格式

1. 扩展 `BatchOperationErrorDTO` 添加新字段
2. 更新 `buildErrorData` 方法填充新字段
3. 更新Excel导出注解配置

## 监控和日志

系统提供详细的操作日志，包括：
- 批量操作开始和结束时间
- 成功和失败数量统计
- 每个交付单的处理结果
- 异常信息和错误原因

日志级别：
- INFO：正常操作流程
- WARN：参数验证失败
- ERROR：系统异常
- DEBUG：详细的处理过程

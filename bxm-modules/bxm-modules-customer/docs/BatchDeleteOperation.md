# 增值交付单批量删除功能

## 概述

批量删除功能提供了对增值交付单的批量逻辑删除操作，支持一次性删除多个符合条件的交付单。该功能设计遵循安全性和可追溯性原则，确保只有满足特定状态条件的交付单才能被删除。

## 功能特性

### 删除条件
批量删除操作只允许删除以下状态的交付单：
- **已关闭交付** (`DELIVERY_CLOSED`)
- **已扣款** (`DEDUCTION_COMPLETED`)  
- **已关闭扣款** (`DEDUCTION_CLOSED`)

### 安全特性
1. **状态验证**：严格验证交付单状态，不符合条件的交付单不允许删除
2. **逻辑删除**：采用逻辑删除方式（设置`is_del=true`），数据可恢复
3. **操作记录**：记录操作人、操作时间等信息，便于审计
4. **异常处理**：详细记录删除失败的原因，支持异常数据导出

### 性能特性
- **批量处理**：支持一次删除多个交付单，提高操作效率
- **事务安全**：每个交付单的删除在独立事务中处理，失败不影响其他记录
- **数量限制**：单次批量删除最多支持1000条记录
- **异常缓存**：失败记录缓存到Redis，支持导出分析

## API接口

### 批量删除接口

**接口地址**：`POST /customer/valueAdded/deliveryOrder/batchDelete`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deliveryOrderNos | List<String> | 是 | 交付单编号列表 |
| operatorId | Long | 是 | 操作人ID |
| operatorName | String | 否 | 操作人姓名 |
| reason | String | 否 | 删除原因 |

**请求示例**：
```bash
curl -X POST "http://localhost:8080/customer/valueAdded/deliveryOrder/batchDelete" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "deliveryOrderNos=VAD2508051430001A1C&deliveryOrderNos=VAD2508051430002A1C&operatorId=1001&operatorName=张三&reason=业务结束，清理已完成的交付单"
```

**响应结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "batchNo": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
    "operationDescription": "批量删除",
    "totalCount": 2,
    "successCount": 1,
    "errorCount": 1,
    "successOrderNos": ["VAD2508051430001A1C"],
    "errorOrderNos": ["VAD2508051430002A1C"],
    "startTime": "2025-08-17 10:30:00",
    "endTime": "2025-08-17 10:30:02",
    "duration": 2000,
    "hasErrors": true,
    "summary": "批量删除完成，总计2条，成功1条，失败1条，可通过批次号[a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6]导出异常数据"
  }
}
```

### 导出异常数据接口

**接口地址**：`GET /customer/valueAdded/deliveryOrder/exportBatchErrors/{batchNo}`

**功能说明**：根据批次号导出批量删除过程中产生的异常数据为Excel文件

## 使用场景

### 1. 业务结束清理
当某个项目或业务周期结束时，需要清理已完成的交付单：
```bash
curl -X POST "http://localhost:8080/customer/valueAdded/deliveryOrder/batchDelete" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "deliveryOrderNos=VAD001&deliveryOrderNos=VAD002&operatorId=1001&operatorName=项目经理&reason=项目结束，清理已完成交付单"
```

### 2. 定期数据清理
定期清理已关闭的交付单，释放存储空间：
```bash
curl -X POST "http://localhost:8080/customer/valueAdded/deliveryOrder/batchDelete" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "deliveryOrderNos=VAD003&deliveryOrderNos=VAD004&operatorId=1001&operatorName=系统管理员&reason=定期数据清理"
```

### 3. 错误数据清理
清理因业务流程错误产生的无效交付单：
```bash
curl -X POST "http://localhost:8080/customer/valueAdded/deliveryOrder/batchDelete" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "deliveryOrderNos=VAD005&operatorId=1001&operatorName=业务人员&reason=清理错误数据"
```

## 错误处理

### 常见错误及解决方案

1. **状态不允许删除**
   - 错误信息：`交付单状态不允许删除，当前状态: 已保存待提交`
   - 解决方案：确保交付单状态为已关闭交付、已扣款或已关闭扣款

2. **交付单不存在**
   - 错误信息：`以下交付单不存在或已被删除: VAD001, VAD002`
   - 解决方案：检查交付单编号是否正确，或交付单是否已被删除

3. **参数验证失败**
   - 错误信息：`操作人ID不能为空`
   - 解决方案：确保提供必填参数

4. **数量超限**
   - 错误信息：`单次批量删除最多支持1000条记录`
   - 解决方案：分批处理，每次不超过1000条

### 异常数据导出

当批量删除过程中出现异常时，系统会：
1. 生成唯一的批次号
2. 将异常信息缓存到Redis（缓存1小时）
3. 提供导出接口下载异常数据Excel文件

异常数据包含以下信息：
- 交付单编号
- 统一社会信用代码
- 集团ID
- 客户名称
- 交付单标题
- 当前状态
- 目标状态
- 操作类型
- 错误信息
- 失败时间
- 操作人

## 技术实现

### 核心流程
1. **参数验证**：验证交付单编号列表、操作人ID等必填参数
2. **数据查询**：批量查询交付单，验证存在性
3. **状态检查**：逐个检查交付单状态是否允许删除
4. **执行删除**：对符合条件的交付单执行逻辑删除
5. **异常处理**：记录删除失败的交付单信息
6. **结果返回**：返回删除结果统计和批次号

### 数据库操作
```sql
-- 逻辑删除操作
UPDATE c_value_added_delivery_order 
SET is_del = 1, 
    update_by = ?, 
    update_time = NOW() 
WHERE delivery_order_no = ? 
  AND is_del = 0 
  AND status IN ('DELIVERY_CLOSED', 'DEDUCTION_COMPLETED', 'DEDUCTION_CLOSED')
```

### 事务处理
- 每个交付单的删除操作在独立事务中执行
- 使用`@Transactional(propagation = Propagation.REQUIRES_NEW)`确保事务隔离
- 单个交付单删除失败不影响其他交付单的处理

## 监控和日志

### 操作日志
系统记录详细的删除操作日志：
```
INFO  - Starting batch delete operation, order count: 5, operator: 张三
DEBUG - Batch delete success for order: VAD001, status: DELIVERY_CLOSED
WARN  - Batch delete failed for order: VAD002, error: 交付单状态不允许删除
INFO  - Batch delete completed: total: 5, success: 4, error: 1, duration: 1500ms
```

### 性能监控
- 记录批量删除的执行时间
- 统计成功率和失败率
- 监控异常数据的产生情况

## 注意事项

1. **不可逆性**：虽然是逻辑删除，但业务上应谨慎操作
2. **权限控制**：确保操作人具有删除权限
3. **状态一致性**：删除前确认交付单状态符合业务规则
4. **数据备份**：重要数据删除前建议先备份
5. **批次限制**：单次操作不超过1000条记录，避免性能问题

## 扩展指南

### 新增删除条件
如需支持其他状态的交付单删除，可在`processBatchDelete`方法中修改`allowedDeleteStatuses`集合：

```java
Set<String> allowedDeleteStatuses = Set.of(
    ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED.getCode(),
    ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode(),
    ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED.getCode(),
    // 新增其他允许删除的状态
    ValueAddedDeliveryOrderStatus.NEW_STATUS.getCode()
);
```

### 自定义删除逻辑
可以通过继承或装饰器模式扩展删除逻辑，添加额外的业务验证或操作记录。
